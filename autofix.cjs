console.log('Fixing inventory bugs...'); 
const fs = require('fs'); 
let content = fs.readFileSync('server/storage.ts', 'utf8'); 
const methods = '  async createStockTransfer(transfer) { const [result] = await db.insert(stockTransfers).values(transfer).returning(); return result; }'; 
const pos = content.lastIndexOf('}'); 
const newContent = content.substring(0, pos) + methods + content.substring(pos); 
fs.writeFileSync('server/storage.ts', newContent, 'utf8'); 
console.log('Storage methods added!'); 
