console.log('Restoring storage file...'); 
const fs = require('fs'); 
let content = fs.readFileSync('server/storage.ts', 'utf8'); 
content = content.split('\\n').slice(0, 2685).join('\\n'); 
content += '\\n\\n  async createStockTransfer(transfer) {\\n    const [result] = await db.insert(stockTransfers).values(transfer).returning();\\n    return result;\\n  }\\n\\n  async createStockTransferItem(item) {\\n    const [result] = await db.insert(stockTransferItems).values(item).returning();\\n    return result;\\n  }\\n\\n  async getStockTransfers() {\\n    return await db.select().from(stockTransfers).orderBy(desc(stockTransfers.createdAt));\\n  }\\n\\n  async getStockTransferById(id) {\\n    const [result] = await db.select().from(stockTransfers).where(eq(stockTransfers.id, id));\\n    return result;\\n  }\\n\\n  async getStockTransferItems(transferId) {\\n    return await db.select().from(stockTransferItems).where(eq(stockTransferItems.transferId, transferId));\\n  }\\n\\n  async createStockMovement(movement) {\\n    const [result] = await db.insert(stockMovements).values(movement).returning();\\n    return result;\\n  }\\n}\\n\\nexport const storage = new DatabaseStorage();'; 
