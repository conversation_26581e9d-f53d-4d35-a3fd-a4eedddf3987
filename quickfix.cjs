const fs = require('fs'); 
let c = fs.readFileSync('server/storage.ts', 'utf8'); 
c = c.split('\\n').slice(0, 2685).join('\\n') + '\\n\\n  async createStockTransfer(transfer) { const [result] = await db.insert(stockTransfers).values(transfer).returning(); return result; }\\n\\n  async getStockTransfers() { return await db.select().from(stockTransfers).orderBy(desc(stockTransfers.createdAt)); }\\n}\\n\\nexport const storage = new DatabaseStorage();'; 
fs.writeFileSync('server/storage.ts', c, 'utf8'); 
console.log('Fixed!'); 
